import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_common/wx_storage.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab3Create/place/models/place_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/utils/location_utils.dart';
import 'package:shoot_z/widgets/permission_dialog.dart';
import 'tab_venue_state.dart';

class TabVenueLogic extends GetxController with WidgetsBindingObserver {
  final TabVenueState state = TabVenueState();
  // final CarouselSliderController carouselController =
  //     CarouselSliderController();
  TextEditingController searchController = TextEditingController(); //球馆搜索框
  var searchText = ''.obs; //搜索框文本状态
  /// 是否正在加载数据
  bool _isLoading = false;
  var page = 1;
  var pageSize = 20;
  var totalRows = 0;
  var timeInterVal = DateTime.now().millisecondsSinceEpoch;
  @override
  void onInit() async {
    super.onInit();
    if (Platform.isAndroid) {
      if (await LocationUtils.instance.checkPermission()) {
        onRefresh();
      } else {
        state.init.value = true;
      }
    } else {
      if (await LocationUtils.instance.requestPermission(Get.context!)) {
        onRefresh();
      } else {
        state.init.value = true;
      }
    }

    state.subscription = BusUtils.instance.on((action) {
      if (EventBusKey.getLocation == action.key) {
        onRefresh();
      }
      if (EventBusKey.bottomBarArenaClick == action.key) {
        onRefresh();
      }
    });
    // 添加搜索框文本监听器
    searchController.addListener(() {
      searchText.value = searchController.text;
    });
  }

  @override
  void onClose() {
    super.onClose();
    state.subscription?.cancel();
  }

  Future<void> onRefresh() async {
    await onRefreshList();
    page = 1;
    await getAllArenaList(true);
  }

  Future<void> loadMore() async {
    if (_isLoading) {
      return;
    }
    if (!hasMore()) {
      return;
    }
    await getAllArenaList(false);
  }

  bool hasMore() {
    return state.allArenaList.length < totalRows;
  }

  Future<void> onRefreshList() async {
    if (!UserManager.instance.isLogin) {
      return;
    }
    if (await LocationUtils.instance.checkPermission() &&
        (DateTime.now().millisecondsSinceEpoch - timeInterVal >= 300000)) {
      timeInterVal = DateTime.now().millisecondsSinceEpoch;
      await LocationUtils.instance.getCurrentPosition();
    }
    final position = LocationUtils.instance.position;
    if (position == null) {
      state.init.value = true;
      return;
    }
    final res = await Api().get(ApiUrl.myArenas, queryParameters: {
      'latitude': '${position.latitude}',
      'longitude': '${position.longitude}',
    });
    // if (state.init.value) {
    //   carouselController.jumpToPage(0);
    // }
    log("message########myArenaList${res.data}");
    state.init.value = true;
    if (res.isSuccessful()) {
      state.myArenaList.value = (res.data as List<dynamic>)
          .map((e) => PlaceModel.fromJson(e))
          .toList();
    }
  }

  Future<void> getAllArenaList(bool isRefresh) async {
    WxLoading.show();
    _isLoading = true;
    log("getAllArenaList!!!!!!!!!!");
    final position = LocationUtils.instance.position;
    Map<String, dynamic> params = {'limit': pageSize, 'page': page};
    if (position != null) {
      params['latitude'] = '${position.latitude}';
      params['longitude'] = '${position.longitude}';
    }
    final res = await Api().get(ApiUrl.arenasList, queryParameters: params);
    // if (state.init.value) {
    //   carouselController.jumpToPage(0);
    // }
    log("message########getAllArenaList${res.data}");
    _isLoading = false;
    state.init.value = true;
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      page += 1;
      final list = (res.data['results'] as List)
          .map((e) => PlaceModel.fromJson(e))
          .toList();
      totalRows = res.data["total"];
      if (isRefresh) {
        state.allArenaList.value = list;
      } else {
        state.allArenaList.addAll(list);
      }
    } else {
      if (isRefresh) {
        state.allArenaList.value = [];
        totalRows = 0;
      }
      WxLoading.showToast(res.message);
    }
  }

  void toAll() {
    if (!LocationUtils.instance.havePermission.value) {
      showLocationDialog();
      return;
    }
    AppPage.to(Routes.place);
  }

  void openSettings() async {
    final result = await WxStorage.instance.getBool('requestPermission');
    if (Platform.isAndroid && result == null) {
      WxStorage.instance.setBool('requestPermission', true);
      if (await LocationUtils.instance.requestPermission(Get.context!)) {
        onRefreshList();
      }
    } else {
      LocationUtils.instance.openSettings();
    }
  }
}
