package com.shootZ.app.shoot_z

import android.content.Context
import android.opengl.GLES20
import android.os.Build
import android.os.Bundle
import android.util.Log
import androidx.annotation.NonNull
import com.shootZ.app.shoot_z.config.ShootzPlugin
import com.shootZ.app.shoot_z.utils.ChannelConfig
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import java.io.File
import java.io.FileInputStream
import java.util.Arrays


class MainActivity: FlutterActivity(){
    private lateinit var plugin: ShootzPlugin
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 确保应用在前台打开
         // window.addFlags(android.view.WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED);
        // 启用大内存模式
        window.addFlags(android.view.WindowManager.LayoutParams.FLAG_SECURE)
        window.setFlags(
            android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
            android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
        )
        // 设置内存分配策略
        System.loadLibrary("flutter")
        setVmSettings()
    }

    private fun setVmSettings() {
        try {
            val vmSettingsClass = Class.forName("io.flutter.embedding.engine.FlutterJNI")
            val setVmSettingsMethod = vmSettingsClass.getDeclaredMethod(
                "setVmSettings", 
                Int::class.javaPrimitiveType, 
                Int::class.javaPrimitiveType
            )
            setVmSettingsMethod.isAccessible = true
            setVmSettingsMethod.invoke(null, 256, 512) // 初始256MB，最大512MB
        } catch (e: Exception) {
            Log.e("MemoryConfig", "Failed to set VM settings", e)
        }
    }

    private val CHANNEL = "performance_info"
    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)


        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler {
                call, result ->
            when (call.method) {
                "getAppChannel" -> {
                    result.success(ChannelConfig.getChannel());
                }
                "releaseResources" -> {
                    // 释放图形资源
                    GLES20.glFinish()  // 确保所有GL命令完成

                    // 手动触发垃圾回收
                    System.gc()

                    // 释放其他可能的资源
                    releaseAdditionalResources()

                    result.success(null)
                }
                "getTotalRAM" -> {
                    try {
                        result.success(getTotalRam())
                    } catch (e: Exception) {
                        result.error("ERROR", "无法获取RAM", e)
                    }
                }
                "getCpuCoreCount" -> {
                    try {
                        result.success(getCpuCoreCount())
                    } catch (e: Exception) {
                        result.error("ERROR", "无法获取CPU核心数", e)
                    }
                }
                "getMaxCpuClockSpeed" -> {
                    try {
                        result.success(getMaxCpuClockSpeed())
                    } catch (e: Exception) {
                        result.error("ERROR", "无法获取CPU频率", e)
                    }
                }
                else -> result.notImplemented()
            }
        }
        // 初始化插件
        plugin = try {
            MainApplication.getInstance().plugin
        } catch (e: Exception) {
            Log.e("MainActivity2", "Failed to get plugin", e)
            return
        }
        // 配置插件通道
        plugin.configure(flutterEngine, this)
    }

    private fun getTotalRam(): Long {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            val memInfo = android.app.ActivityManager.MemoryInfo()
            (getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager).getMemoryInfo(memInfo)
            memInfo.totalMem / (1024 * 1024) // 返回MB单位
        } else {
            // 兼容旧设备
            val reader = java.io.BufferedReader(java.io.InputStreamReader(FileInputStream("/proc/meminfo")))
            var totalMemory: Long = 0
            reader.useLines {
                it.forEach { line ->
                    if (line.startsWith("MemTotal:")) {
                        val memoryValue = line.split("\\s+".toRegex())[1].toLong()
                        totalMemory = memoryValue
                        return@forEach
                    }
                }
            }
            totalMemory
        }
    }

    private fun getCpuCoreCount(): Int {
        return Runtime.getRuntime().availableProcessors()
    }

    private fun getMaxCpuClockSpeed(): Long {
        return try {
            val cpuMaxFreqFile = "/sys/devices/system/cpu/cpu0/cpufreq/cpuinfo_max_freq"
            val reader = File(cpuMaxFreqFile).bufferedReader()
            reader.use {
                it.readLine().toLong() / 1000 // 返回MHz单位
            }
        } catch (e: Exception) {
            // 回退到Build属性
            try {
                Build.SUPPORTED_ABIS.forEach { abi ->
                    if (abi.contains("arm64")) return 2500
                }
                1800 // 默认1.8GHz
            } catch (e: Exception) {
                1800 // 最终默认
            }
        }
    }


        companion object {
            private const val CHANNEL = "flutter/gpu"
        }


        private fun releaseAdditionalResources() {
            try {
                // 释放任何其他可能持有的GPU资源
                GLES20.glFlush()

                // 清除缓冲区
                // (根据你的应用需求添加具体释放逻辑)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }



}
