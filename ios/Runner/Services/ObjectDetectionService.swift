//
//  ObjectDetectionService.swift
//  badminton
//
//  Created by <PERSON><PERSON> on 2025/8/12.
//

import CoreML
import Vision
import UIKit

struct DetectionResult {
    let boundingBox: CGRect // 像素坐标
    let normalizedBox: CGRect // 归一化坐标 (0-1)
    let confidence: Float
    let categoryName: String
    let imageSize: CGSize // 原始图像尺寸
}

struct DetectionResultBundle {
    let inferenceTime: Double
    let detections: [DetectionResult]
    var size: CGSize = .zero
}

protocol ObjectDetectionServiceDelegate: AnyObject {
    func objectDetectionService(_ service: ObjectDetectionService, didFinishDetection resultBundle: DetectionResultBundle)
    func objectDetectionService(_ service: ObjectDetectionService, didFailWithError error: Error)
}

class ObjectDetectionService {
    weak var delegate: ObjectDetectionServiceDelegate?
    
    private var visionModel: VNCoreMLModel?
    private let processingQueue = DispatchQueue(label: "object.detection.queue", qos: .userInitiated)
    
    init(model: String) {
      setupModel(model: model)
    }
    
    // MARK: - Public Methods
    
    func detectObjects(in pixelBuffer: CVPixelBuffer, timeStamps: Int) {
        processingQueue.async { [weak self] in
          self?.performDetection(on: pixelBuffer, timeStamps: timeStamps)
        }
    }
    
    // MARK: - Private Methods
    
    private func getModelURL(for modelPathOrName: String) -> URL? {
        var modelURL: URL?
        
        let lowercasedPath = modelPathOrName.lowercased()
        let fileManager = FileManager.default
        
        if lowercasedPath.hasSuffix(".mlmodel") || lowercasedPath.hasSuffix(".mlpackage") {
            let possibleURL = URL(fileURLWithPath: modelPathOrName)
            if fileManager.fileExists(atPath: possibleURL.path) {
                modelURL = possibleURL
            }
        } else {
            if let compiledURL = Bundle.main.url(forResource: modelPathOrName, withExtension: "mlmodelc") {
                modelURL = compiledURL
            } else if let packageURL = Bundle.main.url(forResource: modelPathOrName, withExtension: "mlpackage") {
                modelURL = packageURL
            }
        }
        
        return modelURL
    }
    
    private func setupModel(model modelPathOrName: String) {
        let modelURL = getModelURL(for: modelPathOrName)
        
        guard let finalModelURL = modelURL else {
            print("❌ 模型文件未找到")
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                self.delegate?.objectDetectionService(self, didFailWithError: DetectionError.modelFileNotFound)
            }
            return
        }
        
        // 尝试加载MLModel
        let model: MLModel
        do {
            model = try MLModel(contentsOf: finalModelURL)
        } catch {
            print("❌ MLModel 加载失败: \(error.localizedDescription)")
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                self.delegate?.objectDetectionService(self, didFailWithError: DetectionError.modelLoadFailed(error.localizedDescription))
            }
            return
        }
        
        // 尝试创建VNCoreMLModel
        do {
            let visionModel = try VNCoreMLModel(for: model)
            visionModel.featureProvider = ThresholdProvider()
            self.visionModel = visionModel
        } catch {
            print("❌ VNCoreMLModel 创建失败: \(error.localizedDescription)")
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                self.delegate?.objectDetectionService(self, didFailWithError: DetectionError.visionModelCreationFailed(error.localizedDescription))
            }
            return
        }
    }
    
  private func performDetection(on pixelBuffer: CVPixelBuffer, timeStamps: Int) {
      guard let model = visionModel else {
          DispatchQueue.main.async { [weak self] in
              guard let self = self else { return }
              self.delegate?.objectDetectionService(self, didFailWithError: DetectionError.modelNotAvailable)
          }
          return
      }
      
      // 在闭包外部获取输入图像尺寸
      let imageSize = CGSize(
//          width: CVPixelBufferGetWidth(pixelBuffer),
//          height: CVPixelBufferGetHeight(pixelBuffer)
          width: CVPixelBufferGetHeight(pixelBuffer),
          height: CVPixelBufferGetWidth(pixelBuffer)
      )
    
      
      let request = VNCoreMLRequest(model: model) { [weak self] request, error in
          guard let self = self else { return }
          
          if let error = error {
              DispatchQueue.main.async {
                  self.delegate?.objectDetectionService(self, didFailWithError: error)
              }
              return
          }
          
          guard let results = request.results as? [VNRecognizedObjectObservation] else {
              DispatchQueue.main.async {
                  self.delegate?.objectDetectionService(self, didFailWithError: DetectionError.invalidResults)
              }
              return
          }
          
        let result = self.processResults(results, imageSize: imageSize, timeStamps: timeStamps)
          DispatchQueue.main.async {
              self.delegate?.objectDetectionService(self, didFinishDetection: result)
          }
      }
      
      // 设置检测参数
      request.imageCropAndScaleOption = .centerCrop
//        .scaleFill
      
      let handler = VNImageRequestHandler(cvPixelBuffer: pixelBuffer, orientation: .left, options: [:])
      
      do {
          try handler.perform([request])
      } catch {
          DispatchQueue.main.async { [weak self] in
              guard let self = self else { return }
              self.delegate?.objectDetectionService(self, didFailWithError: error)
          }
      }
  }
  
  private func processResults(_ observations: [VNRecognizedObjectObservation], imageSize: CGSize, timeStamps: Int) -> DetectionResultBundle {
    let now = Date().timeIntervalSince1970 * 1000 // 当前时间（毫秒）
    let diff = now - Double(timeStamps)           // 毫秒差
//    print(self.formatResults(observations, imageSize: imageSize))
    return DetectionResultBundle(
      inferenceTime: diff,
      detections: self.formatResults(observations, imageSize: imageSize),
      size: imageSize
    )
  }
  
  private func formatResults(_ observations: [VNRecognizedObjectObservation], imageSize: CGSize) -> [DetectionResult] {
    return observations.compactMap { observation in
        guard let topLabel = observation.labels.first,
              topLabel.confidence > 0.4 else { // 设置置信度阈值
            return nil
        }
        
        // Vision框架的坐标系是左下角为原点，需要转换为左上角为原点
        let normalizedBox = CGRect(
            x: observation.boundingBox.origin.x,
            y: 1 - observation.boundingBox.origin.y - observation.boundingBox.height,
            width: observation.boundingBox.size.width,
            height: observation.boundingBox.size.height
        )
        
        // 将归一化坐标转换为实际像素坐标
        let boundingBox = CGRect(
            x: normalizedBox.origin.x * imageSize.width,
            y: normalizedBox.origin.y * imageSize.height,
            width: normalizedBox.size.width * imageSize.width,
            height: normalizedBox.size.height * imageSize.height
        )

        return DetectionResult(
            boundingBox: boundingBox,
            normalizedBox: normalizedBox,
            confidence: topLabel.confidence,
            categoryName: topLabel.identifier,
            imageSize: imageSize
        )
    }
  }
  
}

// MARK: - Error Types

enum DetectionError: Error, LocalizedError {
    case modelFileNotFound
    case modelLoadFailed(String)
    case visionModelCreationFailed(String)
    case modelNotAvailable
    case invalidResults
    case processingFailed
    
    var errorDescription: String? {
        switch self {
        case .modelFileNotFound:
            return "模型文件未找到。请确保模型文件已正确添加到项目中。"
        case .modelLoadFailed(let details):
            return "模型加载失败: \(details)"
        case .visionModelCreationFailed(let details):
            return "Vision模型创建失败: \(details)"
        case .modelNotAvailable:
            return "检测模型不可用"
        case .invalidResults:
            return "检测结果无效"
        case .processingFailed:
            return "检测处理失败"
        }
    }
}
