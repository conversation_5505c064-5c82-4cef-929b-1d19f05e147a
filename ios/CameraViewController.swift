//
//  ViewController.swift
//  Demo
//
//  Created by Apple on 2025/3/21
//
//


import AVFoundation
import MediaPipeTasksVision
import UIKit
import SnapKit
import CoreMedia
import GRDB
import Vision
import Photos
import AVKit
// 获取屏幕的宽度和高度
let screenWidth = UIScreen.main.bounds.width
let screenHeight = UIScreen.main.bounds.height
enum IdentifyStatus {
    case notStarted
    case start
    case pause
}
enum ControlBtnStatus {
    case known
    case nextStep
    case annotation
    case start
}
protocol DataCallbackDelegate: NSObjectProtocol {
    func didReceiveData(_ data: String)
    func didClosePage()
}
class CameraViewController: UIViewController {
    static var fixedTrainingId = "20250514"
    static var fixedSampleVideoUrl = ""
    weak var appDelegate: DataCallbackDelegate?
    var identifyStatus = IdentifyStatus.notStarted
    var controlBtnStatus: ControlBtnStatus = .known {
        didSet {
            switch controlBtnStatus {
            case .known:
                controlBtn.setTitle("知道了", for: .normal)
            case .nextStep:
                controlBtn.setTitle("下一步", for: .normal)
            case .annotation, .start:
                helpBtn.isHidden = true
                controlBtn.isHidden = true
            }
        }
    }
    var startTime: String?
    
    // 控制是否显示目标检测框
    private var shouldDrawDetectionBoxes: Bool = true
    
    // 当前会话的投篮记录数组
    private var currentShootRecords: [ShootEventRecord] = []
    //进球数量
    private var goalCount = 0
    //总投篮数量
    private var totalShootCount = 0
    // 所有历史投篮记录，按会话分组
    private var allShootRecordsBySession: [String: [ShootEventRecord]] = [:]
    //熄屏时间：90秒
    private var screenOffTime:TimeInterval = 90
    // 日志显示视图
    private var logDisplayView: LogDisplayView!
    
    private let backgroundQueue = DispatchQueue(label: "com.google.mediapipe.cameraController.backgroundQueue")
    private let objectDetectorServiceQueue = DispatchQueue(
        label: "com.google.mediapipe.cameraController.objectDetectorServiceQueue",
        attributes: .concurrent)
    // Queuing reads and writes to objectDetectorService using the Apple recommended way
    // as they can be read and written from multiple threads and can result in race conditions.
    private struct Constants {
        static let edgeOffset: CGFloat = 2.0
    }
    
    // AIService - 负责目标检测和进球检测
    private var aiService: AIService?
    
    let previewView = UIView()
    let overlayView = OverlayView()
    let hoopRegionOverlayView = HoopRegionOverlayView()
    lazy var controlBtn:UIButton = {
        let btn = UIButton(type: .system)
        btn.frame = CGRect(x: 0, y: 0, width: 200, height: 50)
        btn.setTitle("知道了", for: .normal)
        btn.setTitleColor(UIColor(hex: "#9393A5"), for: .disabled)
        btn.setTitleColor(.white, for: .normal)
        btn.layer.cornerRadius = 25
        btn.clipsToBounds = true
        btn.setBackgroundImage(UIImage(named: "btn_bg"), for: .normal)
        btn.setBackgroundImage(UIImage(), for: .disabled)
        btn.backgroundColor = UIColor(hex: "#2F2F3B")
        btn.addTarget(self, action: #selector(controlAction(_:)), for: .touchDown)
        view.addSubview(btn)
        btn.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(-40)
            make.width.equalTo(200)
            make.height.equalTo(50)
        }
        return btn
    }()
    lazy var tipsLabel:UILabel = {
        let label = UILabel()
        label.textAlignment = .center
        label.font = UIFont.systemFont(ofSize: 14)
        label.text = "请将手机视野对准篮筐和球场位置"
        label.textColor = UIColor.white
        label.isHidden = true
        return label
    }()
    lazy var corLable:UILabel = {
        let label = UILabel()
        label.textAlignment = .center
        label.font = UIFont.systemFont(ofSize: 14)
        label.text = ""
        label.textColor = UIColor.white
        view.addSubview(label)
        label.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(50)
        }
        return label
    }()
    var maksView = ShootMarks()
    lazy var pointImgV:UIImageView = {
        let imgV = UIImageView()
        view.addSubview(imgV)
        return imgV
    }()
    lazy var pointLabel:UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 10)
        label.textColor = .white
        view.addSubview(label)
        return label
    }()
    lazy var timeLabel:UILabel = {
        let label = UILabel()
        label.textAlignment = .center
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = UIColor.white
        view.addSubview(label)
        label.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(30)
        }
        return label
    }()
    lazy var guideView:UIView = {
        let guidV = UIView.init(frame: CGRect(x: 0, y: 0, width: 200, height: 200))
        view.addSubview(guidV)
        
        let label = UILabel()
        label.numberOfLines = 0
        label.textAlignment = .center
        label.font = .systemFont(ofSize: 16)
        var text = "1.篮筐需带篮网\n2.手机摄像头逆光、球场较暗都会影响识别效果\n3.手机保持静置稳定、不晃动，识别效果会更好\n4.推荐使用支架固定"
        label.textColor = .white
        let style = NSMutableParagraphStyle()
        /// 间隙
        style.lineSpacing = 5
        label.attributedText = NSAttributedString(string: text, attributes: [NSAttributedString.Key.paragraphStyle: style])
        guidV.addSubview(label)
        label.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(-171)
        }
        return guidV
    }()
    lazy var closeBtn:UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(named: "close_btn"), for: .normal)
        btn.addTarget(self, action: #selector(closePage), for: .touchDown)
        return btn
    }()
    lazy var helpBtn:UIButton = {
        let btn = UIButton(type: .custom)
        btn.isHidden = true
        btn.layer.zPosition = 1000
        btn.setImage(UIImage(named: "help_icon"), for: .normal)
        btn.addTarget(self, action: #selector(clickToHelpPage(_:)), for: .touchDown)
        return btn
    }()
    lazy var finishBtn:UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(named: "finish_icon"), for: .normal)
        btn.addTarget(self, action: #selector(finishShooting), for: .touchDown)
        return btn
    }()
    lazy var pauseBtn:UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(named: "pause_icon"), for: .normal)
        btn.addTarget(self, action: #selector(clickToContinue), for: .touchDown)
        return btn
    }()
    lazy var pauseLabel:UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14)
        label.text = "暂停"
        label.textColor = .white
        label.textAlignment = .center
        return label
    }()
    let goalCountLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 58, weight: .bold)
        label.translatesAutoresizingMaskIntoConstraints = false
        label.textAlignment = .right
        label.textColor = .white
        label.text = "0"
        return label
    }()
    let totalCountLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 58, weight: .bold)
        label.translatesAutoresizingMaskIntoConstraints = false
        label.textAlignment = .left
        label.textColor = .white
        label.text = "0"
        return label
    }()
    lazy var shootInfoPanel: UIView = {
        let infoV = UIView()
        infoV.backgroundColor = .clear
        view.addSubview(infoV)
        infoV.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-20)
            make.centerX.equalToSuperview()
            make.width.equalToSuperview()
            make.height.equalTo(80)
        }
        infoV.addSubview(finishBtn)
        finishBtn.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.trailing.equalTo(-40)
            make.width.height.equalTo(52)
        }
        let finishLabel = UILabel()
        finishLabel.font = UIFont.systemFont(ofSize: 14)
        finishLabel.text = "结束"
        finishLabel.textColor = .white
        finishLabel.textAlignment = .center
        infoV.addSubview(finishLabel)
        finishLabel.snp.makeConstraints { make in
            make.top.equalTo(finishBtn.snp.bottom).offset(10)
            make.centerX.equalTo(finishBtn.snp.centerX)
        }
        infoV.addSubview(pauseBtn)
        pauseBtn.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.trailing.equalTo(finishBtn.snp.leading).offset(-34)
            make.width.height.equalTo(52)
        }
        infoV.addSubview(pauseLabel)
        pauseLabel.snp.makeConstraints { make in
            make.top.equalTo(pauseBtn.snp.bottom).offset(10)
            make.centerX.equalTo(pauseBtn.snp.centerX)
        }
        let countV = UIView()
        infoV.addSubview(countV)
        countV.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(3)
            make.centerX.equalToSuperview()
            make.width.equalTo(300)
            make.height.equalToSuperview()
        }
        let lineLabel = UILabel()
        lineLabel.text = "/"
        lineLabel.font = UIFont.systemFont(ofSize: 58, weight: .bold)
        lineLabel.textAlignment = .left
        lineLabel.textColor = .white
        lineLabel.text = "/"
        infoV.addSubview(lineLabel)
        lineLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(3)
            make.centerX.equalToSuperview()
        }
        infoV.addSubview(goalCountLabel)
        goalCountLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(3)
            make.trailing.equalTo(lineLabel.snp.leading).offset(-30)
        }
        infoV.addSubview(totalCountLabel)
        totalCountLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(3)
            make.leading.equalTo(lineLabel.snp.trailing).offset(30)
        }
        return infoV
    }()
    private var _annotationView: UIView?
    var annotationView: UIView {
        if _annotationView == nil {
            let annotationV = UIView()
            view.addSubview(annotationV)
            annotationV.snp.makeConstraints { make in
                make.center.height.equalToSuperview()
                make.width.equalTo(414)
            }
            let startBtn = UIButton(type: .system)
            startBtn.setTitle("开始标注", for: .normal)
            startBtn.setTitleColor(.white, for: .normal)
            startBtn.titleLabel?.font = UIFont.systemFont(ofSize: 14)
            startBtn.addTarget(self, action: #selector(annotationAction), for: .touchUpInside)
            startBtn.setBackgroundImage(UIImage(named: "btn_bg"), for: .normal)
            annotationV.addSubview(startBtn)
            startBtn.snp.makeConstraints { make in
                make.width.equalTo(200)
                make.height.equalTo(50)
                make.leading.equalToSuperview()
                make.bottom.equalToSuperview().offset(-86)
            }
            let skipBtn = UIButton(type: .system)
            skipBtn.setTitle("跳过此步骤", for: .normal)
            skipBtn.addTarget(self, action: #selector(skipAction), for: .touchUpInside)
            skipBtn.layer.cornerRadius = 25
            skipBtn.clipsToBounds = true
            skipBtn.backgroundColor = .white
            skipBtn.setTitleColor(UIColor(hex: "#22222D"), for: .normal)
            skipBtn.titleLabel?.font = UIFont.systemFont(ofSize: 14)
            annotationV.addSubview(skipBtn)
            skipBtn.snp.makeConstraints { make in
                make.width.equalTo(startBtn.snp.width)
                make.height.equalTo(startBtn.snp.height)
                make.trailing.equalToSuperview()
                make.centerY.equalTo(startBtn.snp.centerY)
            }
            let tipsLab = UILabel()
            tipsLab.text = "请根据示意图按照从1~6的顺序在场地中标注六个点"
            tipsLab.font = UIFont.systemFont(ofSize: 14)
            tipsLab.textAlignment = .center
            tipsLab.textColor = .white
            annotationV.addSubview(tipsLab)
            tipsLab.snp.makeConstraints { make in
                make.center.equalToSuperview()
            }
            let titleLab = UILabel()
            titleLab.text = "球场标注打点"
            titleLab.font = UIFont.systemFont(ofSize: 16)
            titleLab.textAlignment = .center
            titleLab.textColor = .white
            annotationV.addSubview(titleLab)
            titleLab.snp.makeConstraints { make in
                make.bottom.equalTo(tipsLab.snp.top).offset(-20)
                make.centerX.equalToSuperview()
            }
            _annotationView = annotationV
        }
        return _annotationView!
    }
//    let cameraUnavailableLabel: UILabel = {
//        let label = UILabel()
//        label.font = .systemFont(ofSize: 17)
//        label.textAlignment = .center
//        label.textColor = .white
//        label.translatesAutoresizingMaskIntoConstraints = false
//        return label
//    }()
//    lazy var resumeButton: UIButton = {
//        let btn = UIButton(type: .system)
//        btn.setTitle("Resum", for: .normal)
//        btn.backgroundColor = .init(hex: "2f2f2f", alpha: 0.3)
//        btn.setTitleColor(.white, for: .normal)
//        btn.contentEdgeInsets = UIEdgeInsets(top: 5, left: 5, bottom: 5, right: 5)
//        btn.translatesAutoresizingMaskIntoConstraints = false
//        btn.addTarget(self, action: #selector(onClickResume(_:)), for: .touchDown)
//        btn.isHidden = true
//        return btn
//    }()
//    let inferenceTimeLabel: UILabel = {
//        let label = UILabel()
//        label.font = .systemFont(ofSize: 14)
//        label.translatesAutoresizingMaskIntoConstraints = false
//        return label
//    }()
//    lazy var countView: UIView = {
//        let view = UIView()
//        view.layer.cornerRadius = 5
//        view.layer.masksToBounds = true
//        view.backgroundColor = .init(hex: "2f2f2f",alpha: 0.3)
//        view.translatesAutoresizingMaskIntoConstraints = false
//        view.addSubview(countLabel)
//        countLabel.snp.makeConstraints { make in
//            make.edges.equalTo(UIEdgeInsets.init(top: 10, left: 10, bottom: 10, right: 10))
//        }
//        return view
//    }()
//    let countLabel: UILabel = {
//        let label = UILabel()
//        label.font = .systemFont(ofSize: 22)
//        label.translatesAutoresizingMaskIntoConstraints = false
//        label.textColor = .white
//        label.text = "0/0"
//        return label
//    }()
//    lazy var startButton: UIButton = {
//        let btn = UIButton(type: .system)
//        btn.setTitle("开始", for: .normal)
//        btn.backgroundColor = .init(hex: "2f2f2f", alpha: 0.3)
//        btn.setTitleColor(.white, for: .normal)
//        btn.titleLabel?.font = .systemFont(ofSize: 20)
//        btn.contentEdgeInsets = UIEdgeInsets(top: 8, left: 8, bottom: 8, right: 8)
//        btn.translatesAutoresizingMaskIntoConstraints = false
//        btn.addTarget(self, action: #selector(onClickStart(_:)), for: .touchDown)
//        return btn
//    }()
//    lazy var endButton: UIButton = {
//        let btn = UIButton(type: .system)
//        btn.setTitle("结束", for: .normal)
//        btn.backgroundColor = .init(hex: "2f2f2f", alpha: 0.3)
//        btn.setTitleColor(.white, for: .normal)
//        btn.contentEdgeInsets = UIEdgeInsets(top: 5, left: 5, bottom: 5, right: 5)
//        btn.translatesAutoresizingMaskIntoConstraints = false
//        btn.addTarget(self, action: #selector(onClickEnd(_:)), for: .touchDown)
//        btn.isHidden = true
//        return btn
//    }()
//    lazy var showButton: UIButton = {
//        let btn = UIButton(type: .system)
//        btn.setTitle("查看进球", for: .normal)
//        btn.backgroundColor = .init(hex: "2f2f2f", alpha: 0.3)
//        btn.setTitleColor(.white, for: .normal)
//        btn.titleLabel?.font = .systemFont(ofSize: 20)
//        btn.contentEdgeInsets = UIEdgeInsets(top: 5, left: 5, bottom: 5, right: 5)
//        btn.translatesAutoresizingMaskIntoConstraints = false
//        btn.addTarget(self, action: #selector(onClickShow(_:)), for: .touchDown)
//        return btn
//    }()
//    lazy var recordButton: UIButton = {
//        let btn = UIButton(type: .system)
//        btn.setTitle("比赛记录", for: .normal)
//        btn.backgroundColor = .init(hex: "2f2f2f", alpha: 0.3)
//        btn.setTitleColor(.white, for: .normal)
//        btn.titleLabel?.font = .systemFont(ofSize: 20)
//        btn.contentEdgeInsets = UIEdgeInsets(top: 5, left: 5, bottom: 5, right: 5)
//        btn.translatesAutoresizingMaskIntoConstraints = false
//        btn.addTarget(self, action: #selector(onClickRecord(_:)), for: .touchDown)
//        return btn
//    }()
//    lazy var toggleDetectionBoxButton: UIButton = {
//        let btn = UIButton(type: .system)
//        btn.setTitle("隐藏检测框", for: .normal)
//        btn.backgroundColor = .init(hex: "2f2f2f", alpha: 0.3)
//        btn.setTitleColor(.white, for: .normal)
//        btn.contentEdgeInsets = UIEdgeInsets(top: 5, left: 5, bottom: 5, right: 5)
//        btn.translatesAutoresizingMaskIntoConstraints = false
//        btn.addTarget(self, action: #selector(onToggleDetectionBox(_:)), for: .touchDown)
//        return btn
//    }()
//    lazy var videoSettingsButton: UIButton = {
//        let btn = UIButton(type: .system)
//        btn.setTitle("视频保存设置", for: .normal)
//        btn.backgroundColor = .init(hex: "2f2f2f", alpha: 0.3)
//        btn.setTitleColor(.white, for: .normal)
//        btn.contentEdgeInsets = UIEdgeInsets(top: 5, left: 5, bottom: 5, right: 5)
//        btn.translatesAutoresizingMaskIntoConstraints = false
//        btn.addTarget(self, action: #selector(onVideoSettings(_:)), for: .touchDown)
//        return btn
//    }()
//    lazy var goalView: UIView = {
//        let view = UIView()
//        view.backgroundColor = .white
//        view.addSubview(goalViewClose)
//        goalViewClose.snp.makeConstraints { make in
//            make.leading.trailing.equalToSuperview()
//            make.height.equalTo(41)
//            make.top.equalTo(5)
//        }
//        view.addSubview(videoView)
//        videoView.snp.makeConstraints { make in
//            make.leading.equalTo(view.safeAreaLayoutGuide).offset(10)
//            make.top.equalTo(goalViewClose.snp.bottom).offset(5)
//            make.bottom.equalToSuperview().offset(-15)
//            make.width.equalTo(350)
//        }
//        view.addSubview(collectionView)
//        collectionView.snp.makeConstraints { make in
//            make.leading.equalTo(videoView.snp.trailing).offset(10)
//            make.top.equalTo(videoView)
//            make.bottom.equalToSuperview()
//            make.trailing.equalToSuperview()
//        }
//        view.addSubview(goalViewCountLabel)
//        goalViewCountLabel.snp.makeConstraints { make in
//            make.leading.equalTo(videoView)
//            make.top.equalTo(10)
//        }
//        view.addSubview(goalViewCountLabel2)
//        goalViewCountLabel2.snp.makeConstraints { make in
//            make.trailing.equalTo(-10)
//            make.top.equalTo(goalViewCountLabel)
//        }
//        return view
//    }()
//    lazy var videoView: UIView = {
//        let view = UIView()
//        view.backgroundColor = .gray
//        let label = UILabel()
//        label.textColor = .black
//        label.text = "视频播放器"
//        view.addSubview(label)
//        label.snp.makeConstraints { make in
//            make.center.equalToSuperview()
//        }
//        
//        return view
//    }()
//    lazy var goalViewClose: UIButton = {
//        let btn = UIButton(type: .system)
//        btn.setImage(UIImage(named: "ic_expand_down"), for: .normal)
////        btn.tintColor = .white
//        btn.translatesAutoresizingMaskIntoConstraints = false
//        btn.addTarget(self, action: #selector(onClickCloseView(_:)), for: .touchDown)
//        return btn
//    }()
//    var goalViewBottom: ConstraintMakerEditable?
//    lazy var collectionView: UICollectionView = {
//        let layout = UICollectionViewFlowLayout()
//        layout.scrollDirection = .horizontal // 关键：横向滚动
//        layout.minimumLineSpacing = 10
//        layout.minimumInteritemSpacing = 10
//        layout.sectionInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 10)
//        layout.estimatedItemSize = .zero
//        let view = UICollectionView(frame: .zero, collectionViewLayout: layout)
//        view.register(GoalCollectionViewCell.self, forCellWithReuseIdentifier: "cell")
//        view.dataSource = self
//        view.delegate = self
////        view.contentInset = .zero
//        view.contentInsetAdjustmentBehavior = .never
//        return view
//    }()
//    let goalViewCountLabel: UILabel = {
//        let label = UILabel()
//        label.font = .systemFont(ofSize: 18)
//        label.translatesAutoresizingMaskIntoConstraints = false
//        label.textColor = .black
//        label.text = "0/0 （命中/投篮）"
//        return label
//    }()
//    let goalViewCountLabel2: UILabel = {
//        let label = UILabel()
//        label.font = .systemFont(ofSize: 18)
//        label.translatesAutoresizingMaskIntoConstraints = false
//        label.textColor = .black
//        label.text = "0/0 （投篮人/投篮）"
//        return label
//    }()
//    let imageView: UIImageView = {
//        let v = UIImageView()
//        v.contentMode = .scaleAspectFit
//        return v
//    }()
//    
//    let hoopImageView: UIImageView = {
//        let v = UIImageView()
//        v.contentMode = .scaleAspectFit
//        v.backgroundColor = UIColor.black.withAlphaComponent(0.3)
//        v.layer.borderColor = UIColor.white.cgColor
//        v.layer.borderWidth = 1.0
//        v.layer.cornerRadius = 8.0
//        v.clipsToBounds = true
//        return v
//    }()
    var blackView: UIView?
    var screenOffTimer: Timer?
    private lazy var cameraFeedService = CameraFeedService(previewView: previewView)
    
    // 视频录制服务
    private var videoRecordingService: VideoRecordingService!
    
    /// 是否可以开始检测
    private var canStartDetection: Bool = false {
        didSet {
            // 更新开始按钮状态
            DispatchQueue.main.async { [weak self] in
//                guard let self = self else { return }
//                self.startButton.isEnabled = self.canStartDetection
//                self.startButton.backgroundColor = self.canStartDetection ? .init(hex: "007F8B") : .gray
                //监测到篮筐
                if  self?.controlBtnStatus == .nextStep{
                    if (self?.canStartDetection == true) {
                        self?.aiService?.isHiddenHoopRegion(true)
                        self?.controlBtn.isEnabled = true
                    }else {
                        if self?.identifyStatus == .notStarted {
                            self?.controlBtn.isEnabled = false
                        }
                        self?.aiService?.isHiddenHoopRegion(false)
                    }
                }
            }
        }
    }
    
#if !targetEnvironment(simulator)
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
//        forceLandscapeRight()
        // 使用CameraFeedService直接配置会话
        cameraFeedService.startLiveCameraSession { [weak self] cameraConfiguration in
            DispatchQueue.main.async {
                switch cameraConfiguration {
                case .success:
                    // 在相机会话成功启动后设置分辨率
                    if let resolution = self?.cameraFeedService.videoResolution {
                        self?.aiService?.setVideoResolution(resolution)
                    }
                case .failed:
                    self?.presentVideoConfigurationErrorAlert()
                case .permissionDenied:
                    self?.presentCameraPermissionsDeniedAlert()
                }
            }
        }
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        // 停止相机会话
        cameraFeedService.stopSession()
    }
    // 锁定屏幕方向为横向
    override var supportedInterfaceOrientations: UIInterfaceOrientationMask {
        return .landscapeRight
    }
//    // 控制是否自动旋转
//    override var shouldAutorotate: Bool {
//        return false
//    }
//    // 设置首选方向为右横向
    override var preferredInterfaceOrientationForPresentation: UIInterfaceOrientation {
        return .landscapeRight
    }
//    // 7. 监听设备旋转，强制保持方向
//    override func viewWillTransition(to size: CGSize, with coordinator: UIViewControllerTransitionCoordinator) {
//        super.viewWillTransition(to: size, with: coordinator)
//        
//        coordinator.animate(alongsideTransition: { _ in
//            self.forceLandscapeRight()
//        })
//    }
    // 6. 强制左横屏的方法
    private func forceLandscapeRight() {
        // 方法1：直接设置设备方向（需要先允许旋转才能生效）
        UIDevice.current.setValue(UIInterfaceOrientation.landscapeRight.rawValue, forKey: "orientation")
        
        // 方法2：通过UIViewController尝试旋转（iOS 16+可能需要）
        if #available(iOS 16.0, *) {
            setNeedsUpdateOfSupportedInterfaceOrientations()
            let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene
            windowScene?.requestGeometryUpdate(.iOS(interfaceOrientations: .landscapeRight))
        }
    }
    
    // 隐藏状态栏
    override var prefersStatusBarHidden: Bool {
        return true
    }
    override func viewDidLoad() {
        super.viewDidLoad()
        // 保持屏幕常亮（防止自动锁屏）
        UIApplication.shared.isIdleTimerDisabled = true
        initView()
        
        // 初始化数据库
        _ = DatabaseManager.shared
        
        // 加载历史投篮记录
        loadHistoricalRecords()
        
        // 设置CameraFeedService代理为自己
        cameraFeedService.delegate = self
        
        // 初始化AIService
        aiService = AIService()
        aiService?.delegate = self
        
        // 向AIService设置视频分辨率
        aiService?.setVideoResolution(cameraFeedService.videoResolution)
        
        // 初始化视频录制服务
        videoRecordingService = VideoRecordingService()
        videoRecordingService.setVideoResolution(cameraFeedService.videoResolution)
        
        // 初始化日志显示视图
//        logDisplayView = addLogDisplayView()
        
        // 记录启动日志
//        LogService.shared.info("应用启动成功，等待篮筐识别...")
        
        // 申请相册权限
        requestPhotoLibraryAccess()
    }
    // 30秒后触发熄屏
    func scheduleScreenOff(after delay: TimeInterval) {
        screenOffTimer?.invalidate() // 取消之前的计时器
        
        screenOffTimer = Timer.scheduledTimer(
            withTimeInterval: delay,
            repeats: false
        ) { [weak self] _ in
            self?.simulateScreenOff()
        }
    }
    
    // 模拟熄屏（调低亮度 + 黑色遮罩）
    func simulateScreenOff() {
        print("模拟熄屏")
        
        // 1. 调低亮度
        UIScreen.main.brightness = 0.0
        
        // 2. 添加黑色遮罩（拦截触摸事件）
        blackView = UIView(frame: UIScreen.main.bounds)
        blackView?.backgroundColor = .black
        blackView?.alpha = 1.0
        
        // 3. 添加点击手势（模拟用户点击亮屏）
        let tapGesture = UITapGestureRecognizer(
            target: self,
            action: #selector(handleScreenTap)
        )
        blackView?.addGestureRecognizer(tapGesture)
        blackView?.isUserInteractionEnabled = true
        
        view.addSubview(blackView!)
    }
    // 防止内存泄漏
    deinit {
        timerManager?.pause()
        // 停止AIService检测
        aiService?.stopDetection()
        screenOffTimer?.invalidate()
    }
    // 用户点击屏幕（模拟亮屏）
    @objc func handleScreenTap() {
        print("用户点击，模拟亮屏")
        
        // 1. 移除黑色遮罩
        blackView?.removeFromSuperview()
        blackView = nil
        
        // 2. 恢复亮度
        UIScreen.main.brightness = 0.5
        
        // 3. 90秒后再次熄屏
        scheduleScreenOff(after: screenOffTime)
    }
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        cameraFeedService.updateVideoPreviewLayer(toFrame: previewView.bounds)
    }
    
    override func viewWillLayoutSubviews() {
        super.viewWillLayoutSubviews()
        cameraFeedService.updateVideoPreviewLayer(toFrame: previewView.bounds)
    }
    func didPerformInference(inferenceTime: Double) {
//        inferenceTimeLabel.text = String(format: "%.2fms", inferenceTime)
    }
#endif
    //MARK: - 弹出电量不足
    func showBatteryWarning(batteryLevel: Float) {
        // 确保不在已经显示警告的情况下重复显示
//        if LowBatteryWarningVC.showedLowBattery {
//            return
//        }
        let warningVC = LowBatteryWarningVC(batteryLevel: batteryLevel)
        warningVC.delegate = self
        self.present(warningVC, animated: false)
    }
    func initView() {
        view.backgroundColor = .white
        previewView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(previewView)
//        previewView.frame = CGRect(x: 0, y: 0, width: screenWidth, height: screenHeight)
        previewView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        view.addSubview(overlayView)
        overlayView.isHidden = true
        overlayView.backgroundColor = .clear
        overlayView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        view.addSubview(hoopRegionOverlayView)
        hoopRegionOverlayView.isHidden = true
        hoopRegionOverlayView.backgroundColor = .clear
        hoopRegionOverlayView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        view.addSubview(guideView)
        guideView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(400)
            make.height.equalToSuperview()
        }
        view.addSubview(closeBtn)
        closeBtn.snp.makeConstraints { make in
            make.trailing.equalTo(-20)
            make.top.equalTo(20)
            make.width.height.equalTo(45)
        }
        if CameraViewController.fixedSampleVideoUrl != "" {
            view.addSubview(helpBtn)
            helpBtn.snp.makeConstraints { make in
                make.leading.equalTo(0)
                make.top.equalTo(65)
                make.width.equalTo(100)
                make.height.equalTo(35)
            }
        }
        view.addSubview(tipsLabel)
        tipsLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(-134)
        }
        controlBtn.isEnabled = true
        self.timerManager = TimerManager()
        // 设置时间更新回调
        timerManager?.onTimeUpdated = {[weak self] (timeString) in
//            print("当前时间: \(timeString)")
            // 在这里更新UI显示
             self?.timeLabel.text = "已投篮时长: \(timeString)"
        }
//        view.addSubview(overlayView)
//        view.addSubview(cameraUnavailableLabel)
//        view.addSubview(resumeButton)
//        view.addSubview(inferenceTimeLabel)
//        view.addSubview(countView)
//        view.addSubview(showButton)
//        view.addSubview(recordButton)
//        view.addSubview(toggleDetectionBoxButton)
//        view.addSubview(videoSettingsButton)
//        overlayView.backgroundColor = .clear
//        overlayView.snp.makeConstraints { make in
//            make.edges.equalToSuperview()
//        }
//        cameraUnavailableLabel.snp.makeConstraints { make in
//            make.leading.equalTo(10)
//            make.trailing.equalTo(10)
//            make.top.equalTo(20)
//        }
//        resumeButton.snp.makeConstraints { make in
//            make.center.equalToSuperview()
//        }
//        countView.snp.makeConstraints { make in
//            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-10)
//            make.centerX.equalToSuperview()
//        }
//        inferenceTimeLabel.snp.makeConstraints { make in
//            make.centerX.equalToSuperview()
//            make.bottom.equalTo(countView.snp.top).offset(-10)
//        }
        
//        view.addSubview(startButton)
//        view.addSubview(endButton)
//        startButton.snp.makeConstraints { make in
//            make.centerY.equalTo(countView)
//            make.leading.equalTo(15)
//        }
//        endButton.snp.makeConstraints { make in
//            make.width.height.equalTo(startButton)
//            make.centerY.equalTo(startButton)
//            make.leading.equalTo(startButton.snp.trailing).offset(20)
//        }
//        showButton.snp.makeConstraints { make in
//            make.trailing.equalTo(-15)
//            make.centerY.equalTo(countView)
//        }
//        recordButton.snp.makeConstraints { make in
//            make.leading.equalTo(15)
//            make.top.equalTo(10)
//        }
//        toggleDetectionBoxButton.snp.makeConstraints { make in
//            make.trailing.equalTo(showButton.snp.leading).offset(-10)
//            make.centerY.equalTo(countView)
//        }
//        videoSettingsButton.snp.makeConstraints { make in
//            make.trailing.equalTo(toggleDetectionBoxButton.snp.leading).offset(-10)
//            make.centerY.equalTo(countView)
//        }
//        view.addSubview(goalView)
//        goalView.snp.makeConstraints { make in
//            make.leading.trailing.equalToSuperview()
//            make.height.equalTo(300)
//            goalViewBottom = make.bottom.equalToSuperview().offset(300)
//        }
//        view.addSubview(imageView)
//        imageView.snp.makeConstraints { make in
//            make.top.trailing.equalToSuperview()
//            make.width.equalTo(100)
//            make.height.equalTo(160)
//        }
        
        // 添加hoopImageView
//        view.addSubview(hoopImageView)
//        hoopImageView.snp.makeConstraints { make in
//            make.top.equalTo(view.safeAreaLayoutGuide).offset(10)
//            make.leading.equalTo(view.safeAreaLayoutGuide).offset(10)
//            make.width.height.equalTo(120)
//        }
    }
    
    
//    @objc func onClickResume(_ sender: Any) {
//        cameraFeedService.resumeInterruptedSession { [weak self] isSessionRunning in
//            if isSessionRunning {
//                self?.resumeButton.isHidden = true
//                self?.cameraUnavailableLabel.isHidden = true
//            }
//        }
//    }
    //MARK: - 跳过此步骤
    @objc func skipAction() {
        let alert = CustomAlertViewController(
            title: "提示",
            message: "跳过标注打点步骤的话，分析结果将不会生成投篮点位图，确定跳过吗?"
        ) {
            self.annotationView.removeFromSuperview()
            self._annotationView = nil
            self.hoopRegionOverlayView.isHidden = false
            self.overlayView.isHidden = false
            self.aiService?.isHiddenHoopRegion(false)
            self.controlBtnStatus = .start
            self.startAction()
        } cancelHandler: {
            print("用户点击了取消")
        }

        
        present(alert, animated: true, completion: nil)
//        let alertController = UIAlertController.init(title: "提示", message: "跳过标注打点步骤的话，分析结果将不会生成投篮点位图，确定跳过吗?", preferredStyle: UIAlertController.Style.alert)
//        let confirmAction = UIAlertAction.init(title: "确定", style: UIAlertAction.Style.default, handler: { (alertAction) in
//            self.annotationView.removeFromSuperview()
//            self._annotationView = nil
//            self.hoopRegionOverlayView.isHidden = false
//            self.overlayView.isHidden = false
//            self.aiService?.isHiddenHoopRegion(false)
//            self.controlBtnStatus = .start
//            self.startAction()
//        })
//        // 取消按钮
//        let cancelAction = UIAlertAction(title: "取消", style: .cancel) { _ in
//            print("用户点击了取消")
//            // 在这里添加取消按钮的操作
//        }
//        alertController.addAction(confirmAction)
//        alertController.addAction(cancelAction)
//        self.present(alertController, animated: true, completion: nil)
    }
    //MARK: - 开始标注
    @objc func annotationAction() {
//        annotationView.isHidden = true
        self.cameraFeedService.captureCurrentFrame()
    }
    @objc func controlAction(_ sender: Any) {
        switch controlBtnStatus {
        case .known:
            guideView.isHidden = true
            helpBtn.isHidden = false
            hoopRegionOverlayView.isHidden = false
            overlayView.isHidden = false
            aiService?.isHiddenHoopRegion(false)
            controlBtnStatus = .nextStep
            controlBtn.isEnabled = false
            tipsLabel.isHidden = false
        case .nextStep:
            tipsLabel.isHidden = true
            if annotationView.superview == nil {
                annotationView.isHidden = false
            }
            hoopRegionOverlayView.isHidden = true
            overlayView.isHidden = true
            aiService?.isHiddenHoopRegion(true)
            controlBtnStatus = .annotation
        default:
            return
        }
    }
    // MARK: - 退出当前页面
    @objc func closePage() {
        // 创建二次确认弹框
        let alertController = UIAlertController(
            title: "确认退出",
            message: "确定要退出当前页面吗？",
            preferredStyle: .alert
        )

        // 取消按钮
        let cancelAction = UIAlertAction(title: "取消", style: .cancel, handler: nil)

        // 确认按钮
        let confirmAction = UIAlertAction(title: "确定", style: .destructive) { [weak self] _ in
            self?.performClosePage()
        }

        alertController.addAction(cancelAction)
        alertController.addAction(confirmAction)

        // 显示弹框
        present(alertController, animated: true, completion: nil)
    }
    @objc func finishShooting() {
        // 创建二次确认弹框
        let alertController = UIAlertController(
            title: "",
            message: "是否确定结束拍摄",
            preferredStyle: .alert
        )

        // 取消按钮
        let cancelAction = UIAlertAction(title: "取消", style: .cancel, handler: nil)

        // 确认按钮
        let confirmAction = UIAlertAction(title: "确定", style: .destructive) { [weak self] _ in
            self?.onClickEnd()
        }

        alertController.addAction(cancelAction)
        alertController.addAction(confirmAction)

        // 显示弹框
        present(alertController, animated: true, completion: nil)
    }
    // MARK: - 执行关闭页面操作
    private func performClosePage() {
        timerManager?.pause()
        // 停止AIService检测
        aiService?.stopDetection()
        appDelegate?.didClosePage()
        self.dismiss(animated: true, completion: nil)
    }
    // MARK: - 帮助教程
    @objc func clickToHelpPage(_ sender: Any) {
        guard let url = URL(string: CameraViewController.fixedSampleVideoUrl) else { return  }
        let player = AVPlayer(url: url)
        let playerViewController = AVPlayerViewController()
        playerViewController.player = player
        
        // 呈现播放器
        present(playerViewController, animated: true) {
            player.play()
        }
    }
    // MARK: - 点击继续
    @objc func clickToContinue() {
        // 检查是否可以开始检测
        guard canStartDetection || identifyStatus != .notStarted else {
            print("错误：在开始检测前必须先检测到有效的篮筐和篮板")
            return
        }
        if identifyStatus == .pause {
            identifyStatus = .start
            self.timerManager?.startOrResume()
            pauseBtnChangeAction()
            aiService?.resumeDetection()
        }else {
            identifyStatus = .pause
            self.timerManager?.pause()
            pauseBtnChangeAction(false)
            aiService?.pauseDetection()
        }
    }
    func pauseBtnChangeAction(_ isPause: Bool = true) {
        if isPause {
            self.pauseBtn.setImage(UIImage(named: "pause_icon"), for: .normal)
            self.pauseLabel.text = "暂停"
        }else {
            self.pauseBtn.setImage(UIImage(named: "resume_icon"), for: .normal)
            self.pauseLabel.text = "继续"
        }
        
    }
//    @objc func onClickStart(_ sender: UIButton) {
//        // 检查是否可以开始检测
//        guard canStartDetection || identifyStatus != .notStarted else {
//            print("错误：在开始检测前必须先检测到有效的篮筐和篮板")
//            return
//        }
//        
//        endButton.isHidden = false
//        if identifyStatus == .notStarted {
//            identifyStatus = .start
//            sender.setTitle("暂停", for: .normal)
//            let formatter = DateFormatter()
//            formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
//            formatter.locale = Locale(identifier: "zh_CN")
//            startTime = formatter.string(from: Date())
//            
//            // 初始化当前会话的投篮记录数组
//            currentShootRecords = []
//            
//            aiService?.startDetection()
//        }
//        else if identifyStatus == .start {
//            identifyStatus = .pause
//            sender.setTitle("继续", for: .normal)
//            
//            aiService?.pauseDetection()
//        }
//        else if identifyStatus == .pause {
//            identifyStatus = .start
//            sender.setTitle("暂停", for: .normal)
//            
//            aiService?.resumeDetection()
//        }
//    }
    func startAction() {
        // 检查是否可以开始检测
        guard canStartDetection || identifyStatus != .notStarted else {
            hoopRegionOverlayView.isHidden = true
            guideView.isHidden = false
            controlBtnStatus = .known
            overlayView.isHidden = true
            controlBtn.isHidden = false
            controlBtn.isEnabled = true
            tipsLabel.isHidden = true
            print("错误：在开始检测前必须先检测到有效的篮筐和篮板")
            return
        }
        if identifyStatus == .notStarted {
            self.timerManager?.startOrResume()
            identifyStatus = .start
            self.shootInfoPanel.backgroundColor = .clear
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
            formatter.locale = Locale(identifier: "zh_CN")
            startTime = formatter.string(from: Date())
            
            // 初始化当前会话的投篮记录数组
            currentShootRecords = []
            
            aiService?.startDetection()
        }
        else if identifyStatus == .start {
            identifyStatus = .pause
            
            aiService?.pauseDetection()
        }
        else if identifyStatus == .pause {
            identifyStatus = .start
            aiService?.resumeDetection()
        }
        BatteryMonitor.shared.startMonitoring(threshold: 0.2) { [weak self] level in
            self?.showBatteryWarning(batteryLevel: level)
        }
    }
    // 声明一个 Timer 实例
//var delayTimer: Timer?
    var timerManager: TimerManager?
    @objc func onClickEnd() {
        identifyStatus = .notStarted
//        startButton.setTitle("开始", for: .normal)
//        endButton.isHidden = true
        
        // 保存当前会话记录到历史记录中
        if let startTime = startTime {
            allShootRecordsBySession[startTime] = currentShootRecords
        }
        
        startTime = nil
        timerManager?.pause()
        appDelegate?.didClosePage()
        // 停止AIService检测
        aiService?.stopDetection()
   
        self.dismiss(animated: true, completion: nil)

    // 设置定时器
    // delayTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: false) { _ in
    //     self.delayMethod()
    //     self.dismiss(animated: true, completion: nil)
    //     self.delayTimer = nil
    // }
    }
    
//    @objc func onClickShow(_ sender: UIButton) {
//        goalViewBottom?.constraint.update(offset: 0)
//        UIView.animate(withDuration: 0.3) {
//            self.view.layoutIfNeeded()
//        }
//    }
//    @objc func onClickCloseView(_ sender: UIButton) {
//        goalViewBottom?.constraint.update(offset: 300)
//        UIView.animate(withDuration: 0.3) {
//            self.view.layoutIfNeeded()
//        }
//    }
    
//    @objc func onClickRecord(_ sender: UIButton) {
//        // 显示记录界面
//        let recordVC = RecordViewController()
//        recordVC.modalPresentationStyle = .fullScreen
//        present(recordVC, animated: true)
//    }
//    
//    @objc func onToggleDetectionBox(_ sender: UIButton) {
//        // 切换目标检测框绘制状态
//        shouldDrawDetectionBoxes = !shouldDrawDetectionBoxes
//        
//        // 更新按钮标题
//        if shouldDrawDetectionBoxes {
//            sender.setTitle("隐藏检测框", for: .normal)
//        } else {
//            sender.setTitle("显示检测框", for: .normal)
//            
//            // 清除当前显示的检测框
//            overlayView.clear()
//        }
//    }
    
//    @objc func onVideoSettings(_ sender: UIButton) {
//        // 创建设置弹窗
//        let alertController = UIAlertController(
//            title: "视频保存设置",
//            message: "选择要保存的投篮视频类型",
//            preferredStyle: .actionSheet
//        )
//        
//        // 添加保存进球视频选项
//        let saveGoalAction = UIAlertAction(title: "保存进球视频", style: .default) { [weak self] _ in
//            self?.toggleSaveGoalVideos()
//        }
//        
//        // 添加保存未进球视频选项
//        let saveNoGoalAction = UIAlertAction(title: "保存未进球视频", style: .default) { [weak self] _ in
//            self?.toggleSaveNoGoalVideos()
//        }
//        
//        // 添加取消按钮
//        let cancelAction = UIAlertAction(title: "取消", style: .cancel)
//        
//        // 检查视频录制服务是否可用
//        if let videoService = videoRecordingService {
//            // 直接访问属性值（通过视频服务实例提供的公共方法）
//            // 设置当前保存进球视频状态的勾选标记
//            videoService.getShouldSaveGoalVideos { shouldSaveGoal in
//                saveGoalAction.setValue(shouldSaveGoal, forKey: "checked")
//            }
//            
//            // 设置当前保存未进球视频状态的勾选标记
//            videoService.getShouldSaveNoGoalVideos { shouldSaveNoGoal in
//                saveNoGoalAction.setValue(shouldSaveNoGoal, forKey: "checked")
//            }
//        }
//        
//        alertController.addAction(saveGoalAction)
//        alertController.addAction(saveNoGoalAction)
//        alertController.addAction(cancelAction)
//        
//        // 在iPad上设置弹出位置
//        if let popoverController = alertController.popoverPresentationController {
//            popoverController.sourceView = sender
//            popoverController.sourceRect = sender.bounds
//        }
//        
//        present(alertController, animated: true)
//    }
    
    // 切换是否保存进球视频
//    private func toggleSaveGoalVideos() {
//        guard let videoService = videoRecordingService else { return }
//        
//        // 通过视频服务获取和修改值
//        videoService.getShouldSaveGoalVideos { currentStatus in
//            let newStatus = !currentStatus
//            videoService.setShouldSaveGoalVideos(newStatus)
//            
//            // 显示提示
//            let message = newStatus ? "已开启进球视频保存" : "已关闭进球视频保存"
//            self.showToast(message: message)
//        }
//    }
    
    // 切换是否保存未进球视频
//    private func toggleSaveNoGoalVideos() {
//        guard let videoService = videoRecordingService else { return }
//        
//        // 通过视频服务获取和修改值
//        videoService.getShouldSaveNoGoalVideos { currentStatus in
//            let newStatus = !currentStatus
//            videoService.setShouldSaveNoGoalVideos(newStatus)
//            
//            // 显示提示
//            let message = newStatus ? "已开启未进球视频保存" : "已关闭未进球视频保存"
//            self.showToast(message: message)
//        }
//    }
//    
    // 显示Toast消息
    private func showToast(message: String) {
        let toastLabel = UILabel()
        toastLabel.backgroundColor = UIColor.black.withAlphaComponent(0.6)
        toastLabel.textColor = UIColor.white
        toastLabel.textAlignment = .center
        toastLabel.font = UIFont.systemFont(ofSize: 14)
        toastLabel.text = message
        toastLabel.alpha = 1.0
        toastLabel.layer.cornerRadius = 10
        toastLabel.clipsToBounds = true
        toastLabel.numberOfLines = 0
        
        view.addSubview(toastLabel)
        toastLabel.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            toastLabel.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            toastLabel.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 70),
            toastLabel.widthAnchor.constraint(lessThanOrEqualTo: view.widthAnchor, constant: -40),
            toastLabel.heightAnchor.constraint(greaterThanOrEqualToConstant: 35)
        ])
        
        // 设置内边距
        toastLabel.layoutMargins = UIEdgeInsets(top: 8, left: 15, bottom: 8, right: 15)
        
        UIView.animate(withDuration: 2.0, delay: 1.0, options: .curveEaseOut) {
            toastLabel.alpha = 0.0
        } completion: { _ in
            toastLabel.removeFromSuperview()
        }
    }
    
    private func presentCameraPermissionsDeniedAlert() {
        let alertController = UIAlertController(
            title: "Camera Permissions Denied",
            message:
                "Camera permissions have been denied for this app. You can change this by going to Settings",
            preferredStyle: .alert)
        
        let cancelAction = UIAlertAction(title: "Cancel", style: .cancel, handler: nil)
        let settingsAction = UIAlertAction(title: "Settings", style: .default) { (action) in
            // UIApplication.shared.open(
            //     URL(string: UIApplication.openSettingsURLString)!, options: [:], completionHandler: nil)
             if let url = URL(string: UIApplication.openSettingsURLString) {
        if #available(iOS 10.0, *) {
            UIApplication.shared.open(url, options: [:], completionHandler: nil)
        } else {
            UIApplication.shared.openURL(url)
        }
    }
        }
        alertController.addAction(cancelAction)
        alertController.addAction(settingsAction)
        
        present(alertController, animated: true, completion: nil)
    }
    
    private func presentVideoConfigurationErrorAlert() {
        let alert = UIAlertController(
            title: "Camera Configuration Failed",
            message: "There was an error while configuring camera.",
            preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default, handler: nil))
        
        self.present(alert, animated: true)
    }
    
    // 从数据库加载历史记录
    private func loadHistoricalRecords() {
        // 获取所有会话开始时间
        let startTimes = DatabaseManager.shared.getAllStartTimes()
        
        // 对每个会话，加载其记录
        for sessionStartTime in startTimes {
            let records = DatabaseManager.shared.getShootEvents(byStartTime: sessionStartTime)
            allShootRecordsBySession[sessionStartTime] = records
        }
    }
    
    // 申请相册访问权限
    private func requestPhotoLibraryAccess() {
        PHPhotoLibrary.requestAuthorization { [weak self] status in
            DispatchQueue.main.async {
                switch status {
                case .authorized:
                    LogService.shared.info("相册访问权限已授权")
                case .denied, .restricted:
                    LogService.shared.warning("相册访问权限被拒绝，视频无法保存到相册")
                    self?.presentPhotoLibraryPermissionsDeniedAlert()
                case .notDetermined:
                    LogService.shared.info("相册权限未确定")
                case .limited:
                    LogService.shared.info("相册访问权限有限，但可以保存视频")
                @unknown default:
                    LogService.shared.warning("相册权限状态未知")
                }
            }
        }
    }
    
    // 显示相册权限被拒绝的提示
    private func presentPhotoLibraryPermissionsDeniedAlert() {
        let alertController = UIAlertController(
            title: "相册访问被拒绝",
            message: "保存视频需要访问您的相册。您可以在设置中更改此权限。",
            preferredStyle: .alert)
        
        let cancelAction = UIAlertAction(title: "取消", style: .cancel, handler: nil)
        let settingsAction = UIAlertAction(title: "设置", style: .default) { (action) in
            // UIApplication.shared.open(
            //     URL(string: UIApplication.openSettingsURLString)!, options: [:], completionHandler: nil)
                 if let url = URL(string: UIApplication.openSettingsURLString) {
        if #available(iOS 10.0, *) {
            UIApplication.shared.open(url, options: [:], completionHandler: nil)
        } else {
            UIApplication.shared.openURL(url)
        }
    }
        }
        
        alertController.addAction(cancelAction)
        alertController.addAction(settingsAction)
        
        present(alertController, animated: true, completion: nil)
    }
}
//MARK: - LowBatteryWarningDelegate
extension CameraViewController : LowBatteryWarningDelegate {
    func didClosePage() {
        self.closePage()
    }
}
//MARK: - AnnotationViewDelegate
extension CameraViewController : AnnotationViewDelegate {
    func marksFinishAction(pointArr: [CGPoint]) {
        DispatchQueue.main.async {
            print("!!!!!!!!!marksFinishAction",pointArr)
            _ = self.aiService?.setupHomography(screenPoints: pointArr, screenSize: CGSize(width: screenWidth, height: screenHeight))
            self.annotationView.removeFromSuperview()
            self._annotationView = nil
            self.hoopRegionOverlayView.isHidden = false
            self.overlayView.isHidden = false
            self.aiService?.isHiddenHoopRegion(false)
            self.controlBtnStatus = .start
            self.startAction()
            
            self.view.addSubview(self.maksView)
//            self.maksView.addSubImage(shoot: true, point: CGPoint(x: 1500,  y: 0))
            // 初始状态：90秒后模拟熄屏
            self.scheduleScreenOff(after: self.screenOffTime)
        }
    }
}
// 实现CameraFeedServiceDelegate，接收视频帧并传递给AIService
extension CameraViewController: CameraFeedServiceDelegate {
    func captureImage(captureImage: UIImage) {
        DispatchQueue.main.async {
            let annotationV = AnnotationView(captureImage: captureImage)
            annotationV.delegate = self
            self.view.addSubview(annotationV)
            self.view.bringSubviewToFront(self.closeBtn)
            annotationV.magnifyAction()
        }
        
    }
    func didOutput(sampleBuffer: CMSampleBuffer, orientation: UIImage.Orientation) {
        // 确保视频分辨率已设置
        if aiService?.videoResolution == .zero {
            aiService?.setVideoResolution(cameraFeedService.videoResolution)
        }
        
        // 将帧传递给AIService处理
        aiService?.processVideoFrame(sampleBuffer: sampleBuffer, orientation: orientation)
        
        // 同时将帧传递给视频录制服务
        if identifyStatus == .start {
            videoRecordingService?.processVideoFrame(sampleBuffer: sampleBuffer)
        }
    }
    
    func sessionWasInterrupted(canResumeManually resumeManually: Bool) {
        // 更新UI
//        if resumeManually {
//            resumeButton.isHidden = false
//        } else {
//            cameraUnavailableLabel.isHidden = false
//        }
        
        // 暂停AIService检测
        aiService?.pauseDetection()
    }
    
    func sessionInterruptionEnded() {
        // 更新UI
//        cameraUnavailableLabel.isHidden = true
//        resumeButton.isHidden = true
        
        // 如果之前正在检测，则恢复检测
        if identifyStatus == .start {
            aiService?.resumeDetection()
        }
    }
    
    func didEncounterSessionRuntimeError() {
        // 显示恢复按钮
//        resumeButton.isHidden = false
        
        // 暂停AIService检测
        aiService?.pauseDetection()
    }
}

// MARK: - AIServiceDelegate
extension CameraViewController: AIServiceDelegate {
    func didDetectHoopAndBackboard(hoopRect: CGRect, backboardRect: CGRect, isValid: Bool) {
        //避免频繁更新，只有当状态不一致时更新
        if isValid != canStartDetection {
            // 更新开始按钮状态
            canStartDetection = isValid
        }
        
        // 使用LogService记录日志
        if isValid {
//            LogService.shared.info("检测到有效的篮筐和篮板，可以开始检测")
        } else if !hoopRect.isEmpty {
//            LogService.shared.warning("检测到篮筐和篮板，但位置不符合要求")
        }
    }
    
    func didDetectShootEvent(event: ShootEvent) {
//        imageView.image = event.playerImage
        // 构建详细的事件信息
        var eventInfo = ""
        
        // 添加投篮时间信息
//        if let shootTime = event.shootTime {
//            eventInfo += "\n投篮时间: \(String(format: "%.2f", shootTime))"
//        } else {
//            eventInfo += "\n未检测到投篮出手"
//        }
        
        // 添加进球信息
        if event.isGoal {
            eventInfo += "[🏀进球]"
//            if let goalTime = event.goalTime {
//                eventInfo += " (时间: \(String(format: "%.2f", goalTime)))"
//            }
            goalCount += 1
        } else {
            eventInfo += "[😈未进球]"
        }
        totalShootCount += 1
        // 添加投篮人信息
        if event.playerImage != nil {
            eventInfo += " ⛹️: \(String(format: "%.2f", event.playerConfidence * 100))%"
        } else {
            eventInfo += " ⛹️❓"
        }
        var customShootCoord:CGPoint?
        if event.shootCoord == nil {
            let randomX = CGFloat.random(in: 0...1500)
            let randomY = CGFloat.random(in: 0...1400)
            //随机生成默认坐标
            customShootCoord = CGPoint(x: randomX, y: randomY)
        }else {
            customShootCoord = event.shootCoord
        }
        // 使用LogService记录日志
        LogService.shared.log(eventInfo, level: event.isGoal ? .info : .warning)
        // 保存投篮事件到数组和数据库
        if let startTime = self.startTime {
            var playerImagePath: String? = nil
            // 如果有投篮人图像，保存到文件系统
            if let playerImage = event.playerImage {
                playerImagePath = DatabaseManager.shared.saveImage(playerImage)
                saveImageToAlbum(imagePath: playerImagePath!) { success, error in
                    if success {
                        LogService.shared.info("投篮人身型图片成功保存到相册: \(URL(string:playerImagePath!)?.lastPathComponent ?? "")")
                    } else {
                        LogService.shared.error("保存身型图片失败: \(error?.localizedDescription ?? "未知错误")")
                    }
                }
            }else {
                LogService.shared.error("没有获取到身型图片")
            }
            
            // 保存视频 - 使用新的视频录制服务
            videoRecordingService?.saveShootEventVideo(isGoal: event.isGoal) {[weak self] filePath in
//                self?.safelyAccessFile(at: filePath) { url, error in
//                    if let error = error {
//                            print("访问文件出错: \(error)")
//                            return
//                    }
//                    print("!!!!!!!!!!文件路径存在",url?.absoluteString)
//                }
                // 创建记录
                let record = ShootEventRecord(
                    startTime: startTime,
                    shootTime: event.shootTime,
                    isGoal: event.isGoal,
                    goalTime: event.goalTime,
                    playerImagePath: playerImagePath,
                    playerConfidence: Double(event.playerConfidence),
                    filePath: filePath,
                    shootCoord: customShootCoord
                )
                
                // 添加到当前会话记录数组
                self?.currentShootRecords.insert(record, at: 0)
                
                // 同时保存到数据库（后台线程）
                DatabaseManager.shared.saveShootEvent(record) { saveEvent in
                    do {
                        //必须要先存入数据库再将数据传给flutter，因为id是数据库来自增实现的
                        let jsonData = try JSONEncoder().encode(saveEvent)
                        if let jsonString = String(data: jsonData, encoding: .utf8) {
                            self?.appDelegate?.didReceiveData(jsonString)
                        }
                    } catch {
                        print("Error encoding JSON: \(error)")
                    }
                }
            }
            
            
            
            
            // 更新UI
            DispatchQueue.main.async {
                self.maksView.addSubImage(shoot: event.isGoal, point: customShootCoord!)
//                self?.collectionView.reloadData()
                
                // 更新计数标签
//                let totalCount = self?.currentShootRecords.count ?? 0
//                let goalCount = self?.currentShootRecords.filter { $0.isGoal }.count ?? 0
//                let goalCount2 = self?.currentShootRecords.filter { $0.playerImagePath != nil }.count ?? 0
                self.goalCountLabel.text = "\(self.goalCount)"
                self.totalCountLabel.text = "\(self.totalShootCount)"
//                self?.goalViewCountLabel.text = "\(goalCount)/\(totalCount) （命中/投篮）"
//                self?.goalViewCountLabel2.text = "\(goalCount2)/\(totalCount) （投篮人/投篮）"
            }
        } else {
            LogService.shared.error("错误：未找到开始时间，无法保存投篮事件")
        }
    }
    func saveImageToAlbum(imagePath: String, completion: @escaping (Bool, Error?) -> Void) {
        // 1. 检查路径对应的图片是否存在
        guard let image = UIImage(contentsOfFile: imagePath) else {
            let error = NSError(domain: "Image not found", code: 404, userInfo: nil)
            completion(false, error)
            return
        }
        
        // 2. 请求相册权限
        PHPhotoLibrary.requestAuthorization { status in
            if status == .denied || status == .restricted {
                let error = NSError(domain: "No permission", code: 403, userInfo: nil)
                DispatchQueue.main.async {
                    completion(false, error)
                }
                return
            }
            
            // 3. 保存图片到相册
            UIImageWriteToSavedPhotosAlbum(image, nil, nil, nil)
            DispatchQueue.main.async {
                completion(true, nil)
            }
        }
    }
    func addSubImage(shoot:Bool, point:CGPoint) {
        let otherpoint = convertToScreenPoint(fromVideoPoint: point, screenSize: CGSize(width: screenWidth, height: screenHeight), videoResolution: CGSize(width: 1920, height: 1080))
        pointImgV.image = UIImage(named: shoot ? "half_shoot2" : "half_shoot3")
        pointImgV.snp.makeConstraints { make in
            make.leading.equalTo(otherpoint.x-5)
            make.top.equalTo(otherpoint.y-5)
            make.height.equalTo(10)
            make.width.equalTo(10)
        }
        pointLabel.text = "(\(String(format: "%.2f", point.x)), \(String(format: "%.2f", point.y)))"
        pointLabel.snp.makeConstraints { make in
            make.leading.equalTo(otherpoint.x-20)
            make.top.equalTo(otherpoint.y+5)
        }
    }
    func convertToScreenPoint(fromVideoPoint point: CGPoint, screenSize: CGSize, videoResolution: CGSize) -> CGPoint {
        let widthScale = screenSize.width / videoResolution.width
        let scaledVideoHeight = videoResolution.height * widthScale
        let verticalPadding = (scaledVideoHeight - screenSize.height) / 2.0

        let xOnScreen = point.x * widthScale
        let yOnScreen = point.y * widthScale - verticalPadding

        return CGPoint(x: xOnScreen, y: yOnScreen)
    }
    
    func didCaptureHoopImage(_ image: UIImage) {
        // 在主线程更新UI
//        DispatchQueue.main.async { [weak self] in
//            self?.hoopImageView.image = image
//        }
    }
    
    
    func shouldDisplayHoopRegion(_ region: CGRect, imageSize: CGSize) {
        // 显示或隐藏篮筐区域
        if region.isEmpty {
            hoopRegionOverlayView.clearDisplay()
            if identifyStatus == .start {
                DispatchQueue.main.async {
                    self.tipsLabel.isHidden = true
                }
            }
        } else {
            if identifyStatus == .start {
                DispatchQueue.main.async {
                    self.tipsLabel.text = "因设备出现移动偏移，请重新校准篮筐"
                    self.tipsLabel.isHidden = false
                }
            }
            hoopRegionOverlayView.setHoopRegion(region, imageSize: imageSize)
        }
    }
    func aiService(_ service: AIService, didDetectObjects result: DetectionResultBundle?, imageSize: CGSize) {
      if let result = result {
          didPerformInference(inferenceTime: result.inferenceTime)
          
          // 只有在shouldDrawDetectionBoxes为true时才绘制检测框
//          if shouldDrawDetectionBoxes {
//              // 确保图像尺寸有效
//              guard imageSize.width > 0 && imageSize.height > 0 else {
//                  LogService.shared.error("错误：无效的图像尺寸")
//                  return
//              }
//              
//              let overlays = OverlayView.objectOverlays(
//                  fromDetections: result.detections,
////                  inferredOnImageOfSize: CGSize(width: 1920, height: 1080),
//                  inferredOnImageOfSize: imageSize,
//                  andOrientation: .up
//              )
//              
//              // 记录检测结果数量
//              if !overlays.isEmpty && identifyStatus == .start {
//  //                LogService.shared.debug("检测到 \(overlays.count) 个目标")
//              }
//              
//              overlayView.draw(
//                  objectOverlays: overlays,
//                  inBoundsOfContentImageOfSize: imageSize,
//                  edgeOffset: Constants.edgeOffset,
//                  imageContentMode: cameraFeedService.videoGravity.contentMode
//              )
//          } else {
//              // 如果不显示检测框，清除覆盖视图
//              overlayView.clear()
//          }
      }
    }
}

extension CameraViewController: UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    // MARK: - UICollectionViewDataSource
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        // 如果有当前会话，则显示当前会话的记录
        if identifyStatus != .notStarted {
            return currentShootRecords.count
        }
//        // 否则，如果有选中的历史会话，则显示该会话的记录
//        else if let startTime = startTime, let records = allShootRecordsBySession[startTime] {
//            return records.count
//        }
//        // 否则，显示最新一组历史记录
//        else if let latestSession = allShootRecordsBySession.keys.sorted(by: >).first, 
//                let records = allShootRecordsBySession[latestSession] {
//            return records.count
//        }
        
        return 0
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "cell", for: indexPath) as! GoalCollectionViewCell
        
        // 根据当前状态选择使用哪个数组的数据
        var records: [ShootEventRecord] = []
        
        if identifyStatus != .notStarted {
            records = currentShootRecords
        } else if let startTime = startTime, let sessionRecords = allShootRecordsBySession[startTime] {
            records = sessionRecords
        } else if let latestSession = allShootRecordsBySession.keys.sorted(by: >).first, 
                  let latestRecords = allShootRecordsBySession[latestSession] {
            records = latestRecords
        }
        
        if indexPath.item < records.count {
            cell.configure(with: records[indexPath.item])
        }
        
        return cell
    }
    
    // MARK: - UICollectionViewDelegateFlowLayout
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: 100, height: 210) // 每个 cell 宽度
    }
    
    // MARK: - UICollectionViewDelegate
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        // 根据当前状态选择使用哪个数组的数据
        var records: [ShootEventRecord] = []
        
        if identifyStatus != .notStarted {
            records = currentShootRecords
        } else if let startTime = startTime, let sessionRecords = allShootRecordsBySession[startTime] {
            records = sessionRecords
        } else if let latestSession = allShootRecordsBySession.keys.sorted(by: >).first, 
                  let latestRecords = allShootRecordsBySession[latestSession] {
            records = latestRecords
        }
        
        if indexPath.item < records.count {
            let record = records[indexPath.item]
            // 这里可以添加点击单个投篮记录的处理逻辑，比如播放视频等
            print("选中的投篮记录: \(record)")
        }
    }
}

